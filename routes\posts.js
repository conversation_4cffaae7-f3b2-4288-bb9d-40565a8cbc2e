const express = require('express');
const router = express.Router();
const { createPost, getPosts, updatePostDecision, getUserPosts, getPublicFeed } = require('../controllers/postController');
const { protect, admin } = require('../middleware/authMiddleware');

router.post('/post', protect, createPost);
router.get('/getPosts', protect, admin, getPosts);
router.get('/my-posts', protect, getUserPosts);
router.get('/feed', protect, getPublicFeed);
router.post('/updatePostDecision', protect, admin, updatePostDecision);

module.exports = router;